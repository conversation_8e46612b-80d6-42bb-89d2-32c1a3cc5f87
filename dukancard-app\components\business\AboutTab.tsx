import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Linking, Alert } from 'react-native';
import { Package, Users, Calendar, Clock, Phone, Mail, MapPin, Truck, Navigation, MessageCircle, Globe } from 'lucide-react-native';
import { BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface AboutTabProps {
  businessData: BusinessDiscoveryData;
  isDark: boolean;
}

export default function AboutTab({ businessData, isDark }: AboutTabProps) {
  const styles = createPublicCardViewStyles(isDark);
  const iconColor = isDark ? '#9CA3AF' : '#6B7280';

  // Helper functions for contact actions
  const handlePhoneCall = () => {
    if (businessData.phone) {
      const phoneNumber = businessData.phone.replace(/\D/g, '');
      Linking.openURL(`tel:${phoneNumber}`).catch(() => {
        Alert.alert('Error', 'Unable to make phone call');
      });
    }
  };

  const handleEmailPress = () => {
    if (businessData.contact_email) {
      const subject = encodeURIComponent(`Inquiry about ${businessData.business_name}`);
      const body = encodeURIComponent(`Hi ${businessData.business_name}, I found your business on Dukancard and would like to know more about your services.`);
      Linking.openURL(`mailto:${businessData.contact_email}?subject=${subject}&body=${body}`).catch(() => {
        Alert.alert('Error', 'Unable to open email client');
      });
    }
  };

  const handleWhatsAppPress = () => {
    if (businessData.whatsapp_number) {
      const formattedNumber = businessData.whatsapp_number.replace(/\D/g, '');
      const whatsappNumber = formattedNumber.startsWith('91') ? formattedNumber : `91${formattedNumber}`;
      const message = encodeURIComponent(`Hi ${businessData.business_name}, I found your business on Dukancard and would like to know more about your services.`);
      Linking.openURL(`https://wa.me/${whatsappNumber}?text=${message}`).catch(() => {
        Alert.alert('Error', 'Unable to open WhatsApp');
      });
    }
  };

  const handleDirectionsPress = () => {
    if (businessData.google_maps_url) {
      Linking.openURL(businessData.google_maps_url).catch(() => {
        Alert.alert('Error', 'Unable to open Google Maps');
      });
    }
  };

  const handleFacebookPress = () => {
    if (businessData.facebook_url) {
      Linking.openURL(businessData.facebook_url).catch(() => {
        Alert.alert('Error', 'Unable to open Facebook');
      });
    }
  };

  const handleInstagramPress = () => {
    if (businessData.instagram_url) {
      Linking.openURL(businessData.instagram_url).catch(() => {
        Alert.alert('Error', 'Unable to open Instagram');
      });
    }
  };

  const formatBusinessHours = () => {
    if (!businessData.business_hours || typeof businessData.business_hours !== 'object') {
      return [];
    }

    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    return days.map((day, index) => {
      const dayData = businessData.business_hours[day];
      const dayName = dayNames[index];

      if (!dayData || !dayData.isOpen) {
        return { day: dayName, hours: 'Closed' };
      }

      const formatTime = (time) => {
        const [hour, minute] = time.split(':');
        const hourNum = parseInt(hour);
        const period = hourNum >= 12 ? 'PM' : 'AM';
        const displayHour = hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum;
        return `${displayHour}:${minute} ${period}`;
      };

      const openTime = formatTime(dayData.openTime);
      const closeTime = formatTime(dayData.closeTime);
      return { day: dayName, hours: `${openTime} - ${closeTime}` };
    });
  };

  const businessHoursData = formatBusinessHours();

  return (
    <ScrollView style={styles.section} showsVerticalScrollIndicator={false}>
      {/* About Bio */}
      {businessData.about_bio && (
        <View style={styles.aboutSection}>
          <Text style={styles.aboutText}>{businessData.about_bio}</Text>
        </View>
      )}

      {/* Business Information Section */}
      <View style={styles.categorySection}>
        <Text style={styles.categoryTitle}>Business Information</Text>
        <View style={styles.aboutTableContainer}>
          {businessData.business_name && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Package color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Business Name</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.business_name}</Text>
            </View>
          )}

          {businessData.member_name && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Users color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Owner Name</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.member_name}</Text>
            </View>
          )}

          {businessData.business_category && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Package color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Category</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.business_category}</Text>
            </View>
          )}

          {businessData.established_year && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Calendar color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Established</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.established_year}</Text>
            </View>
          )}

          <View style={styles.aboutTableRow}>
            <View style={styles.aboutTableLabel}>
              <Clock color={iconColor} size={16} />
              <Text style={styles.aboutTableLabelText}>Business Status</Text>
            </View>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>Open</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Contact Details Section */}
      <View style={styles.categorySection}>
        <Text style={styles.categoryTitle}>Contact Details</Text>
        <View style={styles.aboutTableContainer}>
          {businessData.phone && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Phone color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Phone Number</Text>
              </View>
              <TouchableOpacity onPress={handlePhoneCall} style={styles.callButton}>
                <Phone color="#fff" size={14} />
                <Text style={styles.callButtonText}>Call Now</Text>
              </TouchableOpacity>
            </View>
          )}

          {businessData.contact_email && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Mail color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Email Address</Text>
              </View>
              <TouchableOpacity onPress={handleEmailPress} style={[styles.callButton, { backgroundColor: '#8B5CF6' }]}>
                <Mail color="#fff" size={14} />
                <Text style={styles.callButtonText}>Send Email</Text>
              </TouchableOpacity>
            </View>
          )}

          {(businessData.address_line || businessData.locality || businessData.city) && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <MapPin color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Full Address</Text>
              </View>
              <Text style={styles.aboutTableValue}>
                {[
                  businessData.address_line,
                  businessData.locality,
                  businessData.city,
                  businessData.state,
                  businessData.pincode,
                ].filter(Boolean).join(', ')}
              </Text>
            </View>
          )}

          {businessData.delivery_info && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Truck color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Delivery Info</Text>
              </View>
              <Text style={styles.aboutTableValue}>{businessData.delivery_info}</Text>
            </View>
          )}

          {businessData.google_maps_url && (
            <View style={styles.aboutTableRow}>
              <View style={styles.aboutTableLabel}>
                <Navigation color={iconColor} size={16} />
                <Text style={styles.aboutTableLabelText}>Get Directions</Text>
              </View>
              <TouchableOpacity onPress={handleDirectionsPress} style={[styles.callButton, { backgroundColor: '#3B82F6' }]}>
                <Navigation color="#fff" size={14} />
                <Text style={styles.callButtonText}>Open Maps</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      {/* Business Hours Section */}
      {businessHoursData.length > 0 && (
        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>Business Hours</Text>
          <View style={styles.aboutTableContainer}>
            {businessHoursData.map((item, index) => (
              <View key={index} style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <Clock color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>{item.day}</Text>
                </View>
                <Text style={styles.aboutTableValue}>{item.hours}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Social Media Section */}
      {(businessData.whatsapp_number || businessData.facebook_url || businessData.instagram_url) && (
        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>Social Media</Text>
          <View style={styles.aboutTableContainer}>
            {businessData.whatsapp_number && (
              <View style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <MessageCircle color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>WhatsApp</Text>
                </View>
                <TouchableOpacity onPress={handleWhatsAppPress} style={[styles.callButton, { backgroundColor: '#25D366' }]}>
                  <MessageCircle color="#fff" size={14} />
                  <Text style={styles.callButtonText}>Chat on WhatsApp</Text>
                </TouchableOpacity>
              </View>
            )}

            {businessData.facebook_url && (
              <View style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <Globe color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>Facebook</Text>
                </View>
                <TouchableOpacity onPress={handleFacebookPress} style={[styles.callButton, { backgroundColor: '#1877F2' }]}>
                  <Globe color="#fff" size={14} />
                  <Text style={styles.callButtonText}>Visit Facebook</Text>
                </TouchableOpacity>
              </View>
            )}

            {businessData.instagram_url && (
              <View style={styles.aboutTableRow}>
                <View style={styles.aboutTableLabel}>
                  <Globe color={iconColor} size={16} />
                  <Text style={styles.aboutTableLabelText}>Instagram</Text>
                </View>
                <TouchableOpacity onPress={handleInstagramPress} style={[styles.callButton, { backgroundColor: '#E4405F' }]}>
                  <Globe color="#fff" size={14} />
                  <Text style={styles.callButtonText}>Visit Instagram</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      )}
    </ScrollView>
  );
}
