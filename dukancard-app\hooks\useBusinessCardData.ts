import { useState, useEffect } from 'react';
import {
  fetchBusinessCardData,
  BusinessCardData,
  BusinessProduct,
  BusinessReview,
  ProductSortOption,
  ReviewSortOption,
  fetchBusinessProductsPaginated,
  fetchBusinessReviewsPaginated
} from '../lib/services/businessCardDataService';

export const useBusinessCardData = (businessId: string, businessSlug: string) => {
  const [cardData, setCardData] = useState<BusinessCardData | null>(null);
  const [loadingCardData, setLoadingCardData] = useState(true);

  useEffect(() => {
    const loadCardData = async () => {
      if (!businessId || !businessSlug) return;

      try {
        setLoadingCardData(true);
        const result = await fetchBusinessCardData(businessId, businessSlug);

        if (result.success && result.data) {
          setCardData(result.data);
        } else {
          console.warn('Failed to load card data:', result.error);
        }
      } catch (error) {
        console.error('Error loading card data:', error);
      } finally {
        setLoadingCardData(false);
      }
    };

    loadCardData();
  }, [businessId, businessSlug]);

  return { cardData, loadingCardData };
};

export const useProductsPagination = (businessId: string, initialProducts: BusinessProduct[] = []) => {
  const [allProducts, setAllProducts] = useState<BusinessProduct[]>(initialProducts);
  const [productsPage, setProductsPage] = useState(1);
  const [loadingMoreProducts, setLoadingMoreProducts] = useState(false);
  const [hasMoreProducts, setHasMoreProducts] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<ProductSortOption>('newest');

  // Initialize pagination data
  useEffect(() => {
    if (initialProducts.length > 0) {
      setAllProducts(initialProducts);
    }
  }, [initialProducts]);

  // Load more products
  const loadMoreProducts = async () => {
    if (loadingMoreProducts || !hasMoreProducts) return;

    setLoadingMoreProducts(true);
    try {
      const result = await fetchBusinessProductsPaginated(
        businessId,
        productsPage + 1,
        10,
        searchQuery || null,
        sortBy
      );

      if (result.success && result.data) {
        // Only add products if we actually got new data
        if (result.data.length > 0) {
          setAllProducts(prev => [...prev, ...result.data!]);
          setProductsPage(prev => prev + 1);
        }
        // Update hasMore based on the result
        setHasMoreProducts(result.hasMore || false);
      } else {
        // If there's an error or no data, stop loading more
        setHasMoreProducts(false);
      }
    } catch (error) {
      console.error('Error loading more products:', error);
      setHasMoreProducts(false);
    } finally {
      setLoadingMoreProducts(false);
    }
  };

  // Search products
  const searchProducts = async (query: string) => {
    setSearchQuery(query);
    setProductsPage(1);
    setLoadingMoreProducts(true);

    try {
      const result = await fetchBusinessProductsPaginated(
        businessId,
        1,
        10,
        query || null,
        sortBy
      );

      if (result.success && result.data) {
        setAllProducts(result.data);
        setHasMoreProducts(result.hasMore || false);
      } else {
        setAllProducts([]);
        setHasMoreProducts(false);
      }
    } catch (error) {
      console.error('Error searching products:', error);
      setAllProducts([]);
      setHasMoreProducts(false);
    } finally {
      setLoadingMoreProducts(false);
    }
  };

  // Sort products
  const sortProducts = async (newSortBy: ProductSortOption) => {
    if (newSortBy === sortBy) return;

    setSortBy(newSortBy);
    setProductsPage(1);
    setLoadingMoreProducts(true);

    try {
      const result = await fetchBusinessProductsPaginated(
        businessId,
        1,
        10,
        searchQuery || null,
        newSortBy
      );

      if (result.success && result.data) {
        setAllProducts(result.data);
        setHasMoreProducts(result.hasMore || false);
      } else {
        setAllProducts([]);
        setHasMoreProducts(false);
      }
    } catch (error) {
      console.error('Error sorting products:', error);
      setAllProducts([]);
      setHasMoreProducts(false);
    } finally {
      setLoadingMoreProducts(false);
    }
  };

  return {
    allProducts,
    loadingMoreProducts,
    hasMoreProducts,
    searchQuery,
    sortBy,
    loadMoreProducts,
    searchProducts,
    sortProducts,
  };
};

export const useReviewsPagination = (businessId: string, initialReviews: BusinessReview[] = []) => {
  const [allReviews, setAllReviews] = useState<BusinessReview[]>(initialReviews);
  const [reviewsPage, setReviewsPage] = useState(1);
  const [loadingMoreReviews, setLoadingMoreReviews] = useState(false);
  const [hasMoreReviews, setHasMoreReviews] = useState(true);
  const [sortBy, setSortBy] = useState<ReviewSortOption>('newest');

  // Initialize pagination data
  useEffect(() => {
    if (initialReviews.length > 0) {
      setAllReviews(initialReviews);
    }
  }, [initialReviews]);

  // Load more reviews
  const loadMoreReviews = async () => {
    if (loadingMoreReviews || !hasMoreReviews) return;

    setLoadingMoreReviews(true);
    try {
      const result = await fetchBusinessReviewsPaginated(
        businessId,
        reviewsPage + 1,
        10,
        sortBy
      );

      if (result.success && result.data) {
        // Only add reviews if we actually got new data
        if (result.data.length > 0) {
          setAllReviews(prev => [...prev, ...result.data!]);
          setReviewsPage(prev => prev + 1);
        }
        // Update hasMore based on the result
        setHasMoreReviews(result.hasMore || false);
      } else {
        // If there's an error or no data, stop loading more
        setHasMoreReviews(false);
      }
    } catch (error) {
      console.error('Error loading more reviews:', error);
      setHasMoreReviews(false);
    } finally {
      setLoadingMoreReviews(false);
    }
  };

  // Sort reviews
  const sortReviews = async (newSortBy: ReviewSortOption) => {
    if (newSortBy === sortBy) return;

    setSortBy(newSortBy);
    setReviewsPage(1);
    setLoadingMoreReviews(true);

    try {
      const result = await fetchBusinessReviewsPaginated(
        businessId,
        1,
        10,
        newSortBy
      );

      if (result.success && result.data) {
        setAllReviews(result.data);
        setHasMoreReviews(result.hasMore || false);
      } else {
        setAllReviews([]);
        setHasMoreReviews(false);
      }
    } catch (error) {
      console.error('Error sorting reviews:', error);
      setAllReviews([]);
      setHasMoreReviews(false);
    } finally {
      setLoadingMoreReviews(false);
    }
  };

  return {
    allReviews,
    loadingMoreReviews,
    hasMoreReviews,
    sortBy,
    loadMoreReviews,
    sortReviews,
  };
};
